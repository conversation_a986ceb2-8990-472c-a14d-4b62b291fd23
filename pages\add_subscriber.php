<?php
require_once '../includes/auth.php';
require_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();

$success_msg = $error_msg = "";

// جلب البيانات من جدول equation
$stmt = $db->query("SELECT DISTINCT `تابع` FROM equation WHERE `تابع` != '' ORDER BY `تابع`");
$belongs_to_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $db->query("SELECT DISTINCT `مكان البرج` FROM equation WHERE `مكان البرج` != '' ORDER BY `مكان البرج`");
$tower_location_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $db->query("SELECT DISTINCT `نوع الخدمة` FROM equation WHERE `نوع الخدمة` != '' ORDER BY `نوع الخدمة`");
$service_type_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $db->query("SELECT DISTINCT `اسم الفرع` FROM equation WHERE `اسم الفرع` != '' ORDER BY `اسم الفرع`");
$branch_name_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $db->query("SELECT DISTINCT `مكان الكابينة` FROM equation WHERE `مكان الكابينة` != '' ORDER BY `مكان الكابينة`");
$cabinet_location_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

$stmt = $db->query("SELECT DISTINCT `PORT` FROM equation WHERE `PORT` != '' ORDER BY `PORT`");
$port_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $query = "INSERT INTO subscribers (
            `تسلسل`, `اسم المشترك الثلاثي واللقب`, `رقم الهاتف`, `الباسورد`, 
            `اسم المستخدم`, `باسورد الراوتر`, `ايبي الراوتر`, `باسورد مشاهدة`,
            `يوزر مشاهدة`, `pas`, `user`, `ip`, `SN ONU`, `رقم السكتر`,
            `الملكية`, `port`, `مكان كابينة`, `ملاحظات على المشترك`,
            `الخصم`, `نوع الخدمة`, `تابع الى`, `اسم الفرع`,
            `مكان البرج`, `سعر فواتير`, `تاريخ الانتهاء`, `send`
        ) VALUES (
            :sequence_number, :full_name, :phone_number, :subscriber_password,
            :subscriber_username, :router_password, :router_ip, :view_password,
            :view_username, :pas, :user, :ip, :onu_sn, :sector_number,
            :ownership, :port, :cabinet_location, :subscriber_notes,
            :discount, :service_type, :belongs_to, :branch_name,
            :tower_location, :invoice_price, :expiry_date, :send
        )";
        
        $stmt = $db->prepare($query);
        
        // تعيين القيم
        $stmt->bindParam(':sequence_number', $_POST['sequence_number']);
        $stmt->bindParam(':full_name', $_POST['full_name']);
        $stmt->bindParam(':phone_number', $_POST['phone_number']);
        $stmt->bindParam(':subscriber_password', $_POST['subscriber_password']);
        $stmt->bindParam(':subscriber_username', $_POST['subscriber_username']);
        $stmt->bindParam(':router_password', $_POST['router_password']);
        $stmt->bindParam(':router_ip', $_POST['router_ip']);
        $stmt->bindParam(':view_password', $_POST['view_password']);
        $stmt->bindParam(':view_username', $_POST['view_username']);
        $stmt->bindParam(':pas', $_POST['pas']);
        $stmt->bindParam(':user', $_POST['user']);
        $stmt->bindParam(':ip', $_POST['ip']);
        $stmt->bindParam(':onu_sn', $_POST['onu_sn']);
        $stmt->bindParam(':sector_number', $_POST['sector_number']);
        $stmt->bindParam(':ownership', $_POST['ownership']);
        $stmt->bindParam(':port', $_POST['port']);
        $stmt->bindParam(':cabinet_location', $_POST['cabinet_location']);
        $stmt->bindParam(':subscriber_notes', $_POST['subscriber_notes']);
        $stmt->bindParam(':discount', $_POST['discount']);
        $stmt->bindParam(':service_type', $_POST['service_type']);
        $stmt->bindParam(':belongs_to', $_POST['belongs_to']);
        $stmt->bindParam(':branch_name', $_POST['branch_name']);
        $stmt->bindParam(':tower_location', $_POST['tower_location']);
        $stmt->bindParam(':invoice_price', $_POST['invoice_price']);
        $stmt->bindParam(':expiry_date', $_POST['expiry_date']);
        $stmt->bindParam(':send', 'ارسال');
        
        if ($stmt->execute()) {
            $success_msg = "تم إضافة المشترك بنجاح";
        }
    } catch(PDOException $e) {
        $error_msg = "حدث خطأ أثناء إضافة المشترك: " . $e->getMessage();
    }
}

require_once '../includes/header.php';
?>

<div class="card">
    <div class="card-header">
        <h3 class="mb-0">إضافة مشترك جديد</h3>
    </div>
    <div class="card-body">
        <?php if($success_msg): ?>
            <div class="alert alert-success"><?php echo $success_msg; ?></div>
        <?php endif; ?>
        
        <?php if($error_msg): ?>
            <div class="alert alert-danger"><?php echo $error_msg; ?></div>
        <?php endif; ?>

        <form method="POST" class="needs-validation" novalidate>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="sequence_number" class="form-label">تسلسل</label>
                    <input type="text" class="form-control" id="sequence_number" name="sequence_number" required>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="full_name" class="form-label">اسم المشترك الثلاثي واللقب</label>
                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="phone_number" name="phone_number">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="subscriber_password" class="form-label">الباسورد</label>
                    <input type="text" class="form-control" id="subscriber_password" name="subscriber_password">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="subscriber_username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="subscriber_username" name="subscriber_username">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="router_password" class="form-label">باسورد الراوتر</label>
                    <input type="text" class="form-control" id="router_password" name="router_password">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="router_ip" class="form-label">ايبي الراوتر</label>
                    <input type="text" class="form-control" id="router_ip" name="router_ip">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="view_password" class="form-label">باسورد مشاهدة</label>
                    <input type="text" class="form-control" id="view_password" name="view_password">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="view_username" class="form-label">يوزر مشاهدة</label>
                    <input type="text" class="form-control" id="view_username" name="view_username">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="pas" class="form-label">pas</label>
                    <input type="text" class="form-control" id="pas" name="pas">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="user" class="form-label">user</label>
                    <input type="text" class="form-control" id="user" name="user">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="ip" class="form-label">ip</label>
                    <input type="text" class="form-control" id="ip" name="ip">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="onu_sn" class="form-label">SN ONU</label>
                    <input type="text" class="form-control" id="onu_sn" name="onu_sn">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="sector_number" class="form-label">رقم السكتر</label>
                    <input type="text" class="form-control" id="sector_number" name="sector_number">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="ownership" class="form-label">الملكية</label>
                    <input type="text" class="form-control" id="ownership" name="ownership">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="port" class="form-label">port</label>
                    <select class="form-select" id="port" name="port">
                        <option value="">اختر رقم المنفذ</option>
                        <?php foreach($port_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="cabinet_location" class="form-label">مكان كابينة</label>
                    <select class="form-select" id="cabinet_location" name="cabinet_location">
                        <option value="">اختر مكان الكابينة</option>
                        <?php foreach($cabinet_location_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="subscriber_notes" class="form-label">ملاحظات على المشترك</label>
                    <textarea class="form-control" id="subscriber_notes" name="subscriber_notes" rows="2"></textarea>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="discount" class="form-label">الخصم</label>
                    <select class="form-select" id="discount" name="discount">
                        <option value="">بدون خصم</option>
                        <option value="مجاني">مجاني</option>
                        <option value="VIP">VIP</option>
                        <option value="فواتير">فواتير</option>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="service_type" class="form-label">نوع الخدمة</label>
                    <select class="form-select" id="service_type" name="service_type">
                        <option value="">اختر نوع الخدمة</option>
                        <?php foreach($service_type_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="belongs_to" class="form-label">تابع الى</label>
                    <select class="form-select" id="belongs_to" name="belongs_to">
                        <option value="">اختر التابعية</option>
                        <?php foreach($belongs_to_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="branch_name" class="form-label">اسم الفرع</label>
                    <select class="form-select" id="branch_name" name="branch_name">
                        <option value="">اختر اسم الفرع</option>
                        <?php foreach($branch_name_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="tower_location" class="form-label">مكان البرج</label>
                    <select class="form-select" id="tower_location" name="tower_location">
                        <option value="">اختر مكان البرج</option>
                        <?php foreach($tower_location_options as $option): ?>
                            <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="invoice_price" class="form-label">سعر فواتير</label>
                    <input type="text" class="form-control" id="invoice_price" name="invoice_price">
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                    <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary">إضافة المشترك</button>
                <a href="/nms/index.php" class="btn btn-secondary">رجوع</a>
            </div>
        </form>
    </div>
</div>

<script>
// تفعيل التحقق من صحة النموذج
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault()
                event.stopPropagation()
            }
            form.classList.add('was-validated')
        }, false)
    })
})()
</script>

<?php require_once '../includes/footer.php'; ?>

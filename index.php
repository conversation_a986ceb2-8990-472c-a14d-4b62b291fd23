<?php
require_once 'includes/auth.php';
require_once 'includes/header.php';
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// إحصائيات المشتركين
$stmt = $db->prepare("SELECT COUNT(*) as total FROM subscribers");
$stmt->execute();
$regular_subscribers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

$stmt = $db->prepare("SELECT COUNT(*) as total FROM wifi_subscribers");
$stmt->execute();
$wifi_subscribers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// آخر 5 مشتركين عاديين
$stmt = $db->prepare("SELECT * FROM subscribers ORDER BY تسلسل DESC LIMIT 5");
$stmt->execute();
$latest_regular = $stmt->fetchAll(PDO::FETCH_ASSOC);

// آخر 5 مشتركين واي فاي
$stmt = $db->prepare("SELECT * FROM wifi_subscribers ORDER BY تسلسل DESC LIMIT 5");
$stmt->execute();
$latest_wifi = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid py-4">
    <!-- إحصائيات سريعة -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <div class="display-3 text-primary mb-3">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-primary mb-2"><?php echo number_format($regular_subscribers); ?></h3>
                    <p class="text-muted fs-5">إجمالي المشتركين</p>
                    <div class="d-grid gap-2">
                        <a href="/nms/pages/add_subscriber.php" class="btn btn-primary">إضافة مشترك</a>
                        <a href="/nms/pages/manage_subscribers.php" class="btn btn-outline-primary">إدارة المشتركين</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-body text-center p-4">
                    <div class="display-3 text-primary mb-3">
                        <i class="bi bi-wifi"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-primary mb-2"><?php echo number_format($wifi_subscribers); ?></h3>
                    <p class="text-muted fs-5">مشتركي الواي فاي</p>
                    <a href="/nms/pages/add_wifi_subscriber.php" class="btn btn-primary">إضافة مشترك واي فاي</a>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر المشتركين -->
    <div class="row g-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">آخر المشتركين المضافين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التسلسل</th>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($latest_regular as $subscriber): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($subscriber['تسلسل']); ?></td>
                                    <td><?php echo htmlspecialchars($subscriber['اسم المشترك الثلاثي واللقب']); ?></td>
                                    <td><?php echo htmlspecialchars($subscriber['رقم الهاتف']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">آخر مشتركي الواي فاي</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التسلسل</th>
                                    <th>الاسم</th>
                                    <th>رقم الهاتف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($latest_wifi as $subscriber): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($subscriber['تسلسل']); ?></td>
                                    <td><?php echo htmlspecialchars($subscriber['اسم المشترك الثلاثي واللقب']); ?></td>
                                    <td><?php echo htmlspecialchars($subscriber['رقم الهاتف']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'includes/footer.php';
?>

<?php
session_start();
require_once('../config/database.php');
require_once('../includes/header.php');

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// Initialize search parameters
$search_sequence = isset($_GET['search_sequence']) ? $_GET['search_sequence'] : '';
$search_name = isset($_GET['search_name']) ? $_GET['search_name'] : '';
$search_username = isset($_GET['search_username']) ? $_GET['search_username'] : '';
$search_phone = isset($_GET['search_phone']) ? $_GET['search_phone'] : '';
$search_ip = isset($_GET['search_ip']) ? $_GET['search_ip'] : '';
$search_sector = isset($_GET['search_sector']) ? $_GET['search_sector'] : '';
$search_cabinet = isset($_GET['search_cabinet']) ? $_GET['search_cabinet'] : '';
$sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'تسلسل';
$sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'ASC';
$items_per_page = 20; // عدد العناصر في كل صفحة
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Build the SQL query
$sql = "SELECT تسلسل, `اسم المشترك الثلاثي واللقب`, `رقم الهاتف`, `اسم المستخدم`, `رقم السكتر`, `مكان كابينة`, ip FROM subscribers WHERE 1=1";

if (!empty($search_sequence)) {
    $sql .= " AND تسلسل = :search_sequence";
}
if (!empty($search_name)) {
    $sql .= " AND `اسم المشترك الثلاثي واللقب` LIKE :search_name";
}
if (!empty($search_username)) {
    $sql .= " AND `اسم المستخدم` LIKE :search_username";
}
if (!empty($search_sector)) {
    $sql .= " AND `رقم السكتر` LIKE :search_sector";
}
if (!empty($search_cabinet)) {
    $sql .= " AND `مكان كابينة` LIKE :search_cabinet";
}
if (!empty($search_phone)) {
    $sql .= " AND `رقم الهاتف` LIKE :search_phone";
}
if (!empty($search_ip)) {
    $sql .= " AND ip LIKE :search_ip";
}

// Add sorting and pagination
$count_sql = str_replace("SELECT تسلسل, `اسم المشترك الثلاثي واللقب`, `رقم الهاتف`, `اسم المستخدم`, `رقم السكتر`, `مكان كابينة`, ip", "SELECT COUNT(*)", $sql);
$sql .= " ORDER BY `$sort_by` $sort_order";
$sql .= " LIMIT " . ($current_page - 1) * $items_per_page . ", $items_per_page";

try {
    // Get total count for pagination
    $count_stmt = $db->prepare($count_sql);
    if (!empty($search_sequence)) {
        $count_stmt->bindValue(':search_sequence', $search_sequence, PDO::PARAM_INT);
    }
    if (!empty($search_name)) {
        $count_stmt->bindValue(':search_name', "%$search_name%", PDO::PARAM_STR);
    }
    if (!empty($search_username)) {
        $count_stmt->bindValue(':search_username', "%$search_username%", PDO::PARAM_STR);
    }
    if (!empty($search_sector)) {
        $count_stmt->bindValue(':search_sector', "%$search_sector%", PDO::PARAM_STR);
    }
    if (!empty($search_cabinet)) {
        $count_stmt->bindValue(':search_cabinet', "%$search_cabinet%", PDO::PARAM_STR);
    }
    if (!empty($search_phone)) {
        $count_stmt->bindValue(':search_phone', "%$search_phone%", PDO::PARAM_STR);
    }
    if (!empty($search_ip)) {
        $count_stmt->bindValue(':search_ip', "%$search_ip%", PDO::PARAM_STR);
    }
    $count_stmt->execute();
    $total_items = $count_stmt->fetchColumn();
    $total_pages = ceil($total_items / $items_per_page);

    // Get the actual data
    $stmt = $db->prepare($sql);
    if (!empty($search_sequence)) {
        $stmt->bindValue(':search_sequence', $search_sequence, PDO::PARAM_INT);
    }
    if (!empty($search_name)) {
        $stmt->bindValue(':search_name', "%$search_name%", PDO::PARAM_STR);
    }
    if (!empty($search_username)) {
        $stmt->bindValue(':search_username', "%$search_username%", PDO::PARAM_STR);
    }
    if (!empty($search_sector)) {
        $stmt->bindValue(':search_sector', "%$search_sector%", PDO::PARAM_STR);
    }
    if (!empty($search_cabinet)) {
        $stmt->bindValue(':search_cabinet', "%$search_cabinet%", PDO::PARAM_STR);
    }
    if (!empty($search_phone)) {
        $stmt->bindValue(':search_phone', "%$search_phone%", PDO::PARAM_STR);
    }
    if (!empty($search_ip)) {
        $stmt->bindValue(':search_ip', "%$search_ip%", PDO::PARAM_STR);
    }
    
    $stmt->execute();
    $subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>

<div class="container mt-4">
    <h2 class="mb-4">إدارة المشتركين</h2>
    
    <!-- Advanced Search Form -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>البحث المتقدم</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_sequence">التسلسل</label>
                            <input type="number" class="form-control" id="search_sequence" name="search_sequence" 
                                   value="<?php echo htmlspecialchars($search_sequence); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_name">اسم المشترك</label>
                            <input type="text" class="form-control" id="search_name" name="search_name" 
                                   value="<?php echo htmlspecialchars($search_name); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_username">اسم المستخدم</label>
                            <input type="text" class="form-control" id="search_username" name="search_username"
                                   value="<?php echo htmlspecialchars($search_username); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_sector">رقم السكتر</label>
                            <input type="text" class="form-control" id="search_sector" name="search_sector"
                                   value="<?php echo htmlspecialchars($search_sector); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_cabinet">مكان كابينة</label>
                            <input type="text" class="form-control" id="search_cabinet" name="search_cabinet"
                                   value="<?php echo htmlspecialchars($search_cabinet); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_phone">رقم الهاتف</label>
                            <input type="text" class="form-control" id="search_phone" name="search_phone"
                                   value="<?php echo htmlspecialchars($search_phone); ?>" placeholder="07XX-XXX-XXXX">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="search_ip">IP</label>
                            <input type="text" class="form-control" id="search_ip" name="search_ip"
                                   value="<?php echo htmlspecialchars($search_ip); ?>" placeholder="XXX.XXX.XXX.XXX">
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> بحث
                        </button>
                        <a href="manage_subscribers.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-counterclockwise"></i> إعادة تعييين
                        </a>
                    </div>
                </div>

                <!-- عرض عدد النتائج -->
                <?php if(isset($total_items)): ?>
                <div class="mt-3">
                    <p class="text-muted">عدد النتائج: <?php echo $total_items; ?></p>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Subscribers Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="?sort_by=تسلسل&sort_order=<?php echo $sort_by == 'تسلسل' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>">
                                    تسلسل
                                    <?php if($sort_by == 'تسلسل'): ?>
                                        <i class="fas fa-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by=اسم المشترك الثلاثي واللقب&sort_order=<?php echo $sort_by == 'اسم المشترك الثلاثي واللقب' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>">
                                    اسم المشترك
                                    <?php if($sort_by == 'اسم المشترك الثلاثي واللقب'): ?>
                                        <i class="fas fa-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by=رقم الهاتف&sort_order=<?php echo $sort_by == 'رقم الهاتف' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>">
                                    رقم الهاتف
                                    <?php if($sort_by == 'رقم الهاتف'): ?>
                                        <i class="fas fa-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by=اسم المستخدم&sort_order=<?php echo $sort_by == 'اسم المستخدم' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>">
                                    اسم المستخدم
                                    <?php if($sort_by == 'اسم المستخدم'): ?>
                                        <i class="fas fa-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by=ip&sort_order=<?php echo $sort_by == 'ip' && $sort_order == 'ASC' ? 'DESC' : 'ASC'; ?>">
                                    IP
                                    <?php if($sort_by == 'ip'): ?>
                                        <i class="fas fa-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                    <?php endif; ?>
                                </a>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subscribers as $subscriber): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($subscriber['تسلسل']); ?></td>
                            <td><?php echo htmlspecialchars($subscriber['اسم المشترك الثلاثي واللقب']); ?></td>
                            <td><?php echo htmlspecialchars($subscriber['رقم الهاتف']); ?></td>
                            <td><?php echo htmlspecialchars($subscriber['اسم المستخدم']); ?></td>
                            <td><?php echo htmlspecialchars($subscriber['ip']); ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="view_subscriber.php?id=<?php echo $subscriber['تسلسل']; ?>" 
                                       class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                    <a href="/nms/pages/edit_subscriber.php?id=<?php echo $subscriber['تسلسل']; ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i> تعديل
                                    </a>
                                    <button class="btn btn-sm btn-danger" 
                                            onclick="deleteSubscriber(<?php echo $subscriber['تسلسل']; ?>)">
                                        <i class="bi bi-trash"></i> حذف
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="d-flex justify-content-center mt-4">
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($current_page - 1); ?>&search_sequence=<?php echo htmlspecialchars($search_sequence); ?>&search_name=<?php echo htmlspecialchars($search_name); ?>&search_username=<?php echo htmlspecialchars($search_username); ?>&search_sector=<?php echo htmlspecialchars($search_sector); ?>&search_cabinet=<?php echo htmlspecialchars($search_cabinet); ?>&search_phone=<?php echo htmlspecialchars($search_phone); ?>&search_ip=<?php echo htmlspecialchars($search_ip); ?>&sort_by=<?php echo htmlspecialchars($sort_by); ?>&sort_order=<?php echo htmlspecialchars($sort_order); ?>">السابق</a>
                        </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?php echo ($i == $current_page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search_sequence=<?php echo htmlspecialchars($search_sequence); ?>&search_name=<?php echo htmlspecialchars($search_name); ?>&search_username=<?php echo htmlspecialchars($search_username); ?>&search_sector=<?php echo htmlspecialchars($search_sector); ?>&search_cabinet=<?php echo htmlspecialchars($search_cabinet); ?>&search_phone=<?php echo htmlspecialchars($search_phone); ?>&search_ip=<?php echo htmlspecialchars($search_ip); ?>&sort_by=<?php echo htmlspecialchars($sort_by); ?>&sort_order=<?php echo htmlspecialchars($sort_order); ?>"><?php echo $i; ?></a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($current_page + 1); ?>&search_sequence=<?php echo htmlspecialchars($search_sequence); ?>&search_name=<?php echo htmlspecialchars($search_name); ?>&search_username=<?php echo htmlspecialchars($search_username); ?>&search_sector=<?php echo htmlspecialchars($search_sector); ?>&search_cabinet=<?php echo htmlspecialchars($search_cabinet); ?>&search_phone=<?php echo htmlspecialchars($search_phone); ?>&search_ip=<?php echo htmlspecialchars($search_ip); ?>&sort_by=<?php echo htmlspecialchars($sort_by); ?>&sort_order=<?php echo htmlspecialchars($sort_order); ?>">التالي</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteSubscriber(id) {
    if (confirm('هل أنت متأكد من حذف هذا المشترك؟')) {
        window.location.href = 'delete_subscriber.php?id=' + id;
    }
}
</script>

<?php require_once('../includes/footer.php'); ?>

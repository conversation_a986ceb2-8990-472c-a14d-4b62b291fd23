<?php
session_start();
require_once('../config/database.php');
require_once('../includes/header.php');

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود معرف المشترك
if (!isset($_GET['id'])) {
    header('Location: manage_subscribers.php');
    exit();
}

$subscriber_id = $_GET['id'];

// جلب بيانات المشترك
try {
    $stmt = $db->prepare("SELECT * FROM subscribers WHERE تسلسل = :id");
    $stmt->bindParam(':id', $subscriber_id, PDO::PARAM_INT);
    $stmt->execute();
    $subscriber = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$subscriber) {
        echo '<div class="alert alert-danger">لم يتم العثور على المشترك</div>';
        exit();
    }
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
    exit();
}
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12 mb-3">
            <a href="manage_subscribers.php" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right"></i> العودة إلى قائمة المشتركين
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h3 class="mb-0">تفاصيل المشترك</h3>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">المعلومات الأساسية</h5>
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th width="40%">التسلسل</th>
                                <td><?php echo htmlspecialchars($subscriber['تسلسل']); ?></td>
                            </tr>
                            <tr>
                                <th>اسم المشترك الثلاثي واللقب</th>
                                <td><?php echo htmlspecialchars($subscriber['اسم المشترك الثلاثي واللقب']); ?></td>
                            </tr>
                            <tr>
                                <th>رقم الهاتف</th>
                                <td><?php echo htmlspecialchars($subscriber['رقم الهاتف']); ?></td>
                            </tr>
                            <tr>
                                <th>نوع الخدمة</th>
                                <td><?php echo htmlspecialchars($subscriber['نوع الخدمة'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>تابع الى</th>
                                <td><?php echo htmlspecialchars($subscriber['تابع الى'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>اسم الفرع</th>
                                <td><?php echo htmlspecialchars($subscriber['اسم الفرع'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>الخصم</th>
                                <td><?php echo htmlspecialchars($subscriber['الخصم'] ?? '0'); ?></td>
                            </tr>
                            <tr>
                                <th>سعر فواتير</th>
                                <td><?php echo htmlspecialchars($subscriber['سعر فواتير'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>تاريخ الانتهاء</th>
                                <td><?php echo htmlspecialchars($subscriber['تاريخ الانتهاء'] ?? ''); ?></td>
                            </tr>
                        </tbody>
                    </table>

                    <h5 class="mb-3 mt-4">معلومات الموقع</h5>
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th width="40%">رقم السكتر</th>
                                <td><?php echo htmlspecialchars($subscriber['رقم السكتر'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>الملكية</th>
                                <td><?php echo htmlspecialchars($subscriber['الملكية'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>مكان كابينة</th>
                                <td><?php echo htmlspecialchars($subscriber['مكان كابينة'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>مكان البرج</th>
                                <td><?php echo htmlspecialchars($subscriber['مكان البرج'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>Port</th>
                                <td><?php echo htmlspecialchars($subscriber['port'] ?? ''); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5 class="mb-3">معلومات الدخول والاتصال</h5>
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th width="40%">اسم المستخدم</th>
                                <td><?php echo htmlspecialchars($subscriber['اسم المستخدم']); ?></td>
                            </tr>
                            <tr>
                                <th>الباسورد</th>
                                <td><?php echo htmlspecialchars($subscriber['الباسورد'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>IP</th>
                                <td><?php echo htmlspecialchars($subscriber['ip']); ?></td>
                            </tr>
                            <tr>
                                <th>ايبي الراوتر</th>
                                <td><?php echo htmlspecialchars($subscriber['ايبي الراوتر'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>باسورد الراوتر</th>
                                <td><?php echo htmlspecialchars($subscriber['باسورد الراوتر'] ?? ''); ?></td>
                            </tr>
                        </tbody>
                    </table>

                    <h5 class="mb-3 mt-4">معلومات المشاهدة</h5>
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <th width="40%">يوزر مشاهدة</th>
                                <td><?php echo htmlspecialchars($subscriber['يوزر مشاهدة'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>باسورد مشاهدة</th>
                                <td><?php echo htmlspecialchars($subscriber['باسورد مشاهدة'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>User</th>
                                <td><?php echo htmlspecialchars($subscriber['user'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>Pass</th>
                                <td><?php echo htmlspecialchars($subscriber['pas'] ?? ''); ?></td>
                            </tr>
                            <tr>
                                <th>SN ONU</th>
                                <td><?php echo htmlspecialchars($subscriber['SN ONU'] ?? ''); ?></td>
                            </tr>
                        </tbody>
                    </table>

                    <h5 class="mb-3 mt-4">ملاحظات</h5>
                    <div class="card">
                        <div class="card-body">
                            <?php echo nl2br(htmlspecialchars($subscriber['ملاحظات على المشترك'] ?? 'لا توجد ملاحظات')); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <a href="edit_subscriber.php?id=<?php echo $subscriber_id; ?>" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> تعديل البيانات
                </a>
                <button onclick="confirmDelete(<?php echo $subscriber_id; ?>)" class="btn btn-danger">
                    <i class="bi bi-trash"></i> حذف المشترك
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('هل أنت متأكد من حذف هذا المشترك؟')) {
        window.location.href = 'delete_subscriber.php?id=' + id;
    }
}
</script>

<?php require_once('../includes/footer.php'); ?>

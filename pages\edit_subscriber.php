<?php
session_start();

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: /nms/login.php');
    exit;
}

require_once '../config/database.php';
require_once '../includes/header.php';

// تأكد من وجود معرف المشترك
if (!isset($_GET['id'])) {
    header('Location: /nms/pages/manage_subscribers.php');
    exit;
}

$subscriber_id = $_GET['id'];
$database = new Database();
$db = $database->getConnection();

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $sql = "UPDATE subscribers SET 
            `اسم المشترك الثلاثي واللقب` = :fullname,
            `رقم الهاتف` = :phone,
            `باسورد` = :password,
            `اسم المستخدم` = :username,
            `باسورد الراوتر` = :router_password,
            `ايبي الراوتر` = :router_ip,
            `باسورد مشاهدة` = :view_password,
            `يوزر مشاهدة` = :view_username,
            `SN ONU` = :sn_onu,
            `رقم السكتر` = :sector_number,
            `الملكية` = :ownership,
            `port` = :port,
            `مكان كابينة` = :cabinet_location,
            `ملاحظات على المشترك` = :notes,
            `الخصم` = :discount,
            `نوع الخدمة` = :service_type,
            `تابع الى` = :followed_by,
            `اسم الفرع` = :branch_name,
            `مكان البرج` = :tower_location,
            `سعر فواتير` = :bill_price,
            `تاريخ الانتهاء` = :expiry_date,
            `ip` = :ip,
            `pas` = :pas,
            `user` = :user
            WHERE تسلسل = :id";

        $stmt = $db->prepare($sql);
        
        // ربط القيم
        $stmt->bindParam(':id', $subscriber_id);
        $stmt->bindParam(':fullname', $_POST['fullname']);
        $stmt->bindParam(':phone', $_POST['phone']);
        $stmt->bindParam(':password', $_POST['password']);
        $stmt->bindParam(':username', $_POST['username']);
        $stmt->bindParam(':router_password', $_POST['router_password']);
        $stmt->bindParam(':router_ip', $_POST['router_ip']);
        $stmt->bindParam(':view_password', $_POST['view_password']);
        $stmt->bindParam(':view_username', $_POST['view_username']);
        $stmt->bindParam(':sn_onu', $_POST['sn_onu']);
        $stmt->bindParam(':sector_number', $_POST['sector_number']);
        $stmt->bindParam(':ownership', $_POST['ownership']);
        $stmt->bindParam(':port', $_POST['port']);
        $stmt->bindParam(':cabinet_location', $_POST['cabinet_location']);
        $stmt->bindParam(':notes', $_POST['notes']);
        $stmt->bindParam(':discount', $_POST['discount']);
        $stmt->bindParam(':service_type', $_POST['service_type']);
        $stmt->bindParam(':followed_by', $_POST['followed_by']);
        $stmt->bindParam(':branch_name', $_POST['branch_name']);
        $stmt->bindParam(':tower_location', $_POST['tower_location']);
        $stmt->bindParam(':bill_price', $_POST['bill_price']);
        $stmt->bindParam(':expiry_date', $_POST['expiry_date']);
        $stmt->bindParam(':ip', $_POST['ip']);
        $stmt->bindParam(':pas', $_POST['pas']);
        $stmt->bindParam(':user', $_POST['user']);

        if ($stmt->execute()) {
            echo '<div class="alert alert-success">تم تحديث بيانات المشترك بنجاح</div>';
        }
    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">حدث خطأ أثناء تحديث البيانات</div>';
    }
}

// جلب بيانات المشترك
try {
    $sql = "SELECT * FROM subscribers WHERE تسلسل = :id";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':id', $subscriber_id);
    $stmt->execute();
    $subscriber = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$subscriber) {
        header('Location: manage_subscribers.php');
        exit;
    }
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">حدث خطأ أثناء جلب البيانات</div>';
    exit;
}
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h5>تعديل بيانات المشترك</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="">
                <div class="row g-3">
                    <!-- المعلومات الأساسية -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>الاسم الثلاثي واللقب</label>
                            <input type="text" class="form-control" name="fullname" 
                                   value="<?php echo isset($subscriber['اسم المشترك الثلاثي واللقب']) ? htmlspecialchars($subscriber['اسم المشترك الثلاثي واللقب']) : ''; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="text" class="form-control" name="phone" 
                                   value="<?php echo isset($subscriber['رقم الهاتف']) ? htmlspecialchars($subscriber['رقم الهاتف']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>نوع الخدمة</label>
                            <input type="text" class="form-control" name="service_type" 
                                   value="<?php echo isset($subscriber['نوع الخدمة']) ? htmlspecialchars($subscriber['نوع الخدمة']) : ''; ?>">
                        </div>
                    </div>

                    <!-- معلومات الحساب -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>اسم المستخدم</label>
                            <input type="text" class="form-control" name="username" 
                                   value="<?php echo isset($subscriber['اسم المستخدم']) ? htmlspecialchars($subscriber['اسم المستخدم']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>كلمة المرور</label>
                            <input type="text" class="form-control" name="password" 
                                   value="<?php echo isset($subscriber['باسورد']) ? htmlspecialchars($subscriber['باسورد']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>IP</label>
                            <input type="text" class="form-control" name="ip" 
                                   value="<?php echo htmlspecialchars($subscriber['ip']); ?>">
                        </div>
                    </div>

                    <!-- معلومات الراوتر -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>باسورد الراوتر</label>
                            <input type="text" class="form-control" name="router_password" 
                                   value="<?php echo isset($subscriber['باسورد الراوتر']) ? htmlspecialchars($subscriber['باسورد الراوتر']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>IP الراوتر</label>
                            <input type="text" class="form-control" name="router_ip" 
                                   value="<?php echo isset($subscriber['ايبي الراوتر']) ? htmlspecialchars($subscriber['ايبي الراوتر']) : ''; ?>">
                        </div>
                    </div>

                    <!-- معلومات المشاهدة -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>يوزر مشاهدة</label>
                            <input type="text" class="form-control" name="view_username" 
                                   value="<?php echo isset($subscriber['يوزر مشاهدة']) ? htmlspecialchars($subscriber['يوزر مشاهدة']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>باسورد مشاهدة</label>
                            <input type="text" class="form-control" name="view_password" 
                                   value="<?php echo isset($subscriber['باسورد مشاهدة']) ? htmlspecialchars($subscriber['باسورد مشاهدة']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>User</label>
                            <input type="text" class="form-control" name="user" 
                                   value="<?php echo htmlspecialchars($subscriber['user']); ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Pass</label>
                            <input type="text" class="form-control" name="pas" 
                                   value="<?php echo htmlspecialchars($subscriber['pas']); ?>">
                        </div>
                    </div>

                    <!-- معلومات الموقع -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>رقم السكتر</label>
                            <input type="text" class="form-control" name="sector_number" 
                                   value="<?php echo isset($subscriber['رقم السكتر']) ? htmlspecialchars($subscriber['رقم السكتر']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>الملكية</label>
                            <input type="text" class="form-control" name="ownership" 
                                   value="<?php echo isset($subscriber['الملكية']) ? htmlspecialchars($subscriber['الملكية']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Port</label>
                            <input type="text" class="form-control" name="port" 
                                   value="<?php echo isset($subscriber['port']) ? htmlspecialchars($subscriber['port']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>مكان كابينة</label>
                            <input type="text" class="form-control" name="cabinet_location" 
                                   value="<?php echo isset($subscriber['مكان كابينة']) ? htmlspecialchars($subscriber['مكان كابينة']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>مكان البرج</label>
                            <input type="text" class="form-control" name="tower_location" 
                                   value="<?php echo isset($subscriber['مكان البرج']) ? htmlspecialchars($subscriber['مكان البرج']) : ''; ?>">
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>SN ONU</label>
                            <input type="text" class="form-control" name="sn_onu" 
                                   value="<?php echo isset($subscriber['SN ONU']) ? htmlspecialchars($subscriber['SN ONU']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>تابع الى</label>
                            <input type="text" class="form-control" name="followed_by" 
                                   value="<?php echo isset($subscriber['تابع الى']) ? htmlspecialchars($subscriber['تابع الى']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>اسم الفرع</label>
                            <input type="text" class="form-control" name="branch_name" 
                                   value="<?php echo isset($subscriber['اسم الفرع']) ? htmlspecialchars($subscriber['اسم الفرع']) : ''; ?>">
                        </div>
                    </div>

                    <!-- المعلومات المالية -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>الخصم</label>
                            <input type="number" step="0.01" class="form-control" name="discount" 
                                   value="<?php echo isset($subscriber['الخصم']) ? htmlspecialchars($subscriber['الخصم']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>سعر فواتير</label>
                            <input type="number" step="0.01" class="form-control" name="bill_price" 
                                   value="<?php echo isset($subscriber['سعر فواتير']) ? htmlspecialchars($subscriber['سعر فواتير']) : ''; ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>تاريخ الانتهاء</label>
                            <input type="date" class="form-control" name="expiry_date" 
                                   value="<?php echo isset($subscriber['تاريخ الانتهاء']) ? htmlspecialchars($subscriber['تاريخ الانتهاء']) : ''; ?>">
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    <div class="col-12">
                        <div class="form-group">
                            <label>ملاحظات على المشترك</label>
                            <textarea class="form-control" name="notes" rows="3"><?php echo isset($subscriber['ملاحظات على المشترك']) ? htmlspecialchars($subscriber['ملاحظات على المشترك']) : ''; ?></textarea>
                        </div>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="col-12 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save"></i> حفظ التعديلات
                        </button>
                        <a href="/nms/pages/manage_subscribers.php" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>

<?php
if(!isset($_SESSION['user_id'])) {
    header("Location: /nms/login.php");
    exit();
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشتركين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #0d6efd;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        .navbar-brand {
            color: white !important;
            font-weight: bold;
        }
        .nav-link {
            color: rgba(255,255,255,.9) !important;
            padding: 0.7rem 1rem !important;
            border-radius: 4px;
            margin: 0 0.2rem;
        }
        .nav-link:hover {
            color: white !important;
            background-color: rgba(255,255,255,.1);
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,.1);
        }
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
        .btn-primary {
            background-color: #0d6efd;
            border: none;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg mb-4">
        <div class="container">
            <a class="navbar-brand" href="index.php">نظام إدارة المشتركين</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/nms/index.php">
                            <i class="bi bi-house-door"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="subscribersDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-people"></i>
                            المشتركين
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="/nms/pages/add_subscriber.php">
                                    <i class="bi bi-person-plus"></i>
                                    إضافة مشترك جديد
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/nms/pages/add_wifi_subscriber.php">
                                    <i class="bi bi-wifi"></i>
                                    إضافة مشترك واي فاي
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/nms/pages/manage_subscribers.php">
                                    <i class="bi bi-list-ul"></i>
                                    إدارة المشتركين
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="nav-link">مرحباً <?php echo htmlspecialchars($_SESSION['full_name']); ?></span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/nms/logout.php">تسجيل الخروج</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="container"><?php if(isset($error_msg)): ?>
        <div class="alert alert-danger"><?php echo $error_msg; ?></div>
    <?php endif; ?>
    <?php if(isset($success_msg)): ?>
        <div class="alert alert-success"><?php echo $success_msg; ?></div>
    <?php endif; ?>

<?php
require_once '../includes/auth.php';
require_once '../config/database.php';

$database = new Database();
$db = $database->getConnection();
$success_msg = '';
$error_msg = '';

// جلب البيانات من جدول equation
$stmt = $db->prepare("SELECT DISTINCT `مكان الكابينة` FROM equation WHERE `مكان الكابينة` != '' ORDER BY `مكان الكابينة`");
$stmt->execute();
$cabinet_location_options = $stmt->fetchAll(PDO::FETCH_COLUMN);

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $query = "INSERT INTO wifi_subscribers (
            `تسلسل`, `اسم المشترك الثلاثي واللقب`, `رقم الهاتف`, 
            `يوزر`, `ايبي الراوتر`, `ايبي النانو`, `رقم السكتر`,
            `الملكية`, `مكان كابينة`, `ملاحظات على المشترك`,
            `باسورد رئيسي`, `يوزر رئيسي`, `باسورد`
        ) VALUES (
            :sequence_number, :full_name, :phone_number,
            :username, :router_ip, :nano_ip, :sector_number,
            :ownership, :cabinet_location, :notes,
            :main_password, :main_username, :password
        )";
        
        $stmt = $db->prepare($query);
        
        $stmt->bindParam(':sequence_number', $_POST['sequence_number']);
        $stmt->bindParam(':full_name', $_POST['full_name']);
        $stmt->bindParam(':phone_number', $_POST['phone_number']);
        $stmt->bindParam(':username', $_POST['username']);
        $stmt->bindParam(':router_ip', $_POST['router_ip']);
        $stmt->bindParam(':nano_ip', $_POST['nano_ip']);
        $stmt->bindParam(':sector_number', $_POST['sector_number']);
        $stmt->bindParam(':ownership', $_POST['ownership']);
        $stmt->bindParam(':cabinet_location', $_POST['cabinet_location']);
        $stmt->bindParam(':notes', $_POST['notes']);
        $stmt->bindParam(':main_password', $_POST['main_password']);
        $stmt->bindParam(':main_username', $_POST['main_username']);
        $stmt->bindParam(':password', $_POST['password']);
        
        if ($stmt->execute()) {
            $success_msg = "تم إضافة مشترك الواي فاي بنجاح";
        }
    } catch(PDOException $e) {
        $error_msg = "حدث خطأ أثناء إضافة المشترك: " . $e->getMessage();
    }
}

require_once '../includes/header.php';
?>

<div class="container mt-5">
    <h2 class="mb-4">إضافة مشترك واي فاي جديد</h2>
    
    <?php if ($success_msg): ?>
        <div class="alert alert-success"><?php echo $success_msg; ?></div>
    <?php endif; ?>
    
    <?php if ($error_msg): ?>
        <div class="alert alert-danger"><?php echo $error_msg; ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="sequence_number" class="form-label">تسلسل</label>
                <input type="number" class="form-control" id="sequence_number" name="sequence_number" max="9999" required>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="full_name" class="form-label">اسم المشترك الثلاثي واللقب</label>
                <input type="text" class="form-control" id="full_name" name="full_name" maxlength="30" required>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="phone_number" class="form-label">رقم الهاتف</label>
                <input type="text" class="form-control" id="phone_number" name="phone_number" maxlength="10" placeholder="07xxxxxxxx">
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="password" class="form-label">باسورد</label>
                <input type="text" class="form-control" id="password" name="password" maxlength="8">
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="username" class="form-label">يوزر</label>
                <input type="text" class="form-control" id="username" name="username" maxlength="19">
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="router_ip" class="form-label">ايبي الراوتر</label>
                <input type="text" class="form-control" id="router_ip" name="router_ip" maxlength="16">
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="main_password" class="form-label">باسورد رئيسي</label>
                <input type="text" class="form-control" id="main_password" name="main_password" maxlength="11">
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="main_username" class="form-label">يوزر رئيسي</label>
                <input type="text" class="form-control" id="main_username" name="main_username" maxlength="4">
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="nano_ip" class="form-label">ايبي النانو</label>
                <input type="text" class="form-control" id="nano_ip" name="nano_ip" maxlength="12">
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="sector_number" class="form-label">رقم السكتر</label>
                <input type="text" class="form-control" id="sector_number" name="sector_number" maxlength="9">
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="ownership" class="form-label">الملكية</label>
                <select class="form-select" id="ownership" name="ownership">
                    <option value="">اختر الملكية</option>
                    <option value="للشبكة">للشبكة</option>
                    <option value="موقع">موقع</option>
                </select>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="cabinet_location" class="form-label">مكان كابينة</label>
                <select class="form-select" id="cabinet_location" name="cabinet_location">
                    <option value="">اختر مكان الكابينة</option>
                    <?php foreach($cabinet_location_options as $option): ?>
                        <option value="<?php echo htmlspecialchars($option); ?>"><?php echo htmlspecialchars($option); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="row">

            
            <div class="col-md-8 mb-3">
                <label for="notes" class="form-label">ملاحظات على المشترك</label>
                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <button type="submit" class="btn btn-primary">إضافة المشترك</button>
            </div>
        </div>
    </form>
</div>

<?php require_once '../includes/footer.php'; ?>
